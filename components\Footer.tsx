
export default function Footer() {
  return (
    <footer className="bg-gray-50 border-t border-gray-200 text-gray-600 text-sm py-16 lg:py-20 xl:py-24 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 lg:gap-10 xl:gap-12 mb-12 lg:mb-16 xl:mb-20">
          {/* MATCHES */}
          <div>
            <h4 className="font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide">MATCHES</h4>
            <ul className="space-y-3 lg:space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">About Us</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Careers</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Affiliates</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Press</a></li>
            </ul>
          </div>

          {/* Customer Care */}
          <div>
            <h4 className="font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide">Customer Care</h4>
            <ul className="space-y-3 lg:space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Contact Us</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Size Guide</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Delivery</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Returns</a></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide">Services</h4>
            <ul className="space-y-3 lg:space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Personal Shopping</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Gift Cards</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Loyalty Program</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Style Advice</a></li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide">Legal</h4>
            <ul className="space-y-3 lg:space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Terms & Conditions</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Privacy Policy</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Cookie Policy</a></li>
            </ul>
          </div>

          {/* Connect */}
          <div>
            <h4 className="font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide">Connect</h4>
            <ul className="space-y-3 lg:space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Instagram</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Twitter</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Facebook</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Pinterest</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-gray-200 pt-8 lg:pt-10 xl:pt-12">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6 lg:gap-8 xl:gap-10 text-center lg:text-left">
            <div className="text-xs text-gray-500 order-1 lg:order-1">
              © 2024 MATCHES. All rights reserved.
            </div>

            <div className="text-xs text-gray-500 order-3 lg:order-2">
              Shipping worldwide | Customer service: +44 (0)20 7647 8888
            </div>

            <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-6 order-2 lg:order-3">
              <span className="text-xs text-gray-500 whitespace-nowrap">Download our app:</span>
              <div className="flex gap-2 sm:gap-3">
                <a href="#" className="bg-black text-white px-3 sm:px-4 py-2 text-xs hover:bg-gray-800 transition-colors">App Store</a>
                <a href="#" className="bg-black text-white px-3 sm:px-4 py-2 text-xs hover:bg-gray-800 transition-colors">Google Play</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
