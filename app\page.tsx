import Link from 'next/link'
import Image from 'next/image'

export default function Home() {
  return (
    <>
      {/* Hero Banner */}
      <section className="relative bg-gray-50 min-h-[80vh] flex items-center py-24 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 items-center">
            <div className="space-y-8 lg:space-y-10">
              <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-light text-black leading-tight tracking-tight">
                NEW SEASON<br />HEROES
              </h1>
              <p className="text-lg lg:text-xl text-gray-600 max-w-lg leading-relaxed">
                Discover the latest arrivals from your favorite designers
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/women" className="bg-black text-white px-8 py-3 text-sm font-medium tracking-wide hover:bg-gray-800 transition-colors text-center">
                  SHOP WOMEN
                </Link>
                <Link href="/men" className="border-2 border-black text-black px-8 py-3 text-sm font-medium tracking-wide hover:bg-black hover:text-white transition-colors text-center">
                  SHOP MEN
                </Link>
              </div>
            </div>
            <div className="relative h-[400px] lg:h-[500px] xl:h-[600px]">
              <Image
                src="/hero-image.jpg"
                alt="New Season Heroes"
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-32 lg:py-40 bg-white">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <div className="text-center mb-20 lg:mb-24">
            <h2 className="text-3xl lg:text-4xl font-light text-black mb-8 tracking-wide">SHOP BY CATEGORY</h2>
            <div className="w-16 h-px bg-black mx-auto"></div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 lg:gap-8">
            {[
              { name: 'CLOTHING', count: '545 PRODUCTS', image: '/category-clothing.jpg' },
              { name: 'SHOES', count: '234 PRODUCTS', image: '/category-shoes.jpg' },
              { name: 'BAGS', count: '156 PRODUCTS', image: '/category-bags.jpg' },
              { name: 'ACCESSORIES', count: '89 PRODUCTS', image: '/category-accessories.jpg' },
              { name: 'SALE', count: 'UP TO 70% OFF', image: '/category-sale.jpg' }
            ].map((category) => (
              <Link key={category.name} href={`/${category.name.toLowerCase()}`} className="group text-center">
                <div className="relative aspect-[4/5] mb-6 overflow-hidden bg-gray-50">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <h3 className="text-sm font-semibold text-black mb-2 tracking-wide">{category.name}</h3>
                <p className="text-xs text-gray-500 uppercase tracking-wide">{category.count}</p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Editorial Content */}
      <section className="py-32 lg:py-40 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20">
            <div className="relative aspect-[3/4] group overflow-hidden">
              <Image
                src="/editorial-1.jpg"
                alt="Editorial Content"
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              <div className="absolute bottom-12 left-12 text-white">
                <h3 className="text-2xl lg:text-3xl font-light mb-4 tracking-wide">WINTER ESSENTIALS</h3>
                <p className="text-base mb-8 opacity-90">Discover our curated edit</p>
                <Link href="/winter-essentials" className="text-sm font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all">
                  SHOP NOW
                </Link>
              </div>
            </div>
            <div className="space-y-8">
              <div className="relative aspect-[4/3] group overflow-hidden">
                <Image
                  src="/editorial-2.jpg"
                  alt="Editorial Content"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-8 left-8 text-white">
                  <h3 className="text-xl lg:text-2xl font-light mb-4 tracking-wide">THE GOLDEN HOUR</h3>
                  <Link href="/golden-hour" className="text-sm font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all">
                    DISCOVER
                  </Link>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-6">
                {[
                  { name: 'DRESSES', image: '/mini-1.jpg' },
                  { name: 'KNITWEAR', image: '/mini-2.jpg' },
                  { name: 'OUTERWEAR', image: '/mini-3.jpg' }
                ].map((item) => (
                  <Link key={item.name} href={`/${item.name.toLowerCase()}`} className="group text-center">
                    <div className="relative aspect-[4/5] mb-4 overflow-hidden bg-gray-100">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <p className="text-xs font-semibold tracking-wide text-black">{item.name}</p>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-32 lg:py-40 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-light mb-12 tracking-wide">SIGN UP TO OUR EMAILS</h2>
            <p className="text-gray-300 mb-16 text-lg leading-relaxed">
              Be the first to know about new arrivals, exclusive offers and style inspiration
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-6 py-4 bg-transparent border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:border-white text-base"
              />
              <button className="bg-white text-black px-8 py-4 font-semibold tracking-wide hover:bg-gray-100 transition-colors text-sm">
                SIGN UP
              </button>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}