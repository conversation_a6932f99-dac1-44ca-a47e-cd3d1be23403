
'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronDownIcon, HeartIcon, ShareIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

const product = {
  id: 1,
  name: 'Moon oversized cotton-poplin shirt',
  brand: 'The Row',
  price: '£1,200',
  description: 'An elegant statement of The Row\'s exacting, tailoring, this cream Moon shirt is cut from crisp cotton-poplin to an oversized fit with clean-lined details. Shown here with The Row Enzo straight-leg leather trousers, The Row Celine calf-hair-lined leather loafers and The Row Celine calf-hair-lined leather loafers.',
  details: [
    'Oversized fit',
    'Button-front closure',
    'Spread collar',
    'Long sleeves with button cuffs',
    'Curved hem',
    'Made in Italy'
  ],
  sizeGuide: 'Size Guide',
  deliveryReturns: 'Delivery and Returns',
  images: [
    '/products/the-row-shirt-1.jpg',
    '/products/the-row-shirt-2.jpg',
    '/products/the-row-shirt-3.jpg',
    '/products/the-row-shirt-4.jpg',
    '/products/the-row-shirt-5.jpg',
    '/products/the-row-shirt-6.jpg'
  ],
  sizes: ['XS', 'S', 'M', 'L', 'XL'],
  colors: [
    { name: 'Cream', value: '#F5F5DC' },
    { name: 'White', value: '#FFFFFF' },
    { name: 'Black', value: '#000000' }
  ]
}

const recentlyViewed = [
  {
    id: 2,
    name: 'Moon oversized cotton-poplin shirt',
    brand: 'The Row',
    price: '£1,200',
    image: '/products/the-row-shirt-recent.jpg'
  }
]

export default function ProductPage() {
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedColor, setSelectedColor] = useState(product.colors[0])
  const [selectedImage, setSelectedImage] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [showSizeGuide, setShowSizeGuide] = useState(false)
  const [showDelivery, setShowDelivery] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <nav className="text-sm text-gray-500">
            <Link href="/" className="hover:text-black">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/women" className="hover:text-black">Women</Link>
            <span className="mx-2">/</span>
            <Link href="/women/designers" className="hover:text-black">Designers</Link>
            <span className="mx-2">/</span>
            <span className="text-black">The Row</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Product Images */}
          <div className="space-y-6">
            {/* Main Image */}
            <div className="relative aspect-[3/4] bg-gray-50">
              <Image
                src={product.images[selectedImage]}
                alt={product.name}
                fill
                className="object-cover"
                priority
              />
            </div>

            {/* Thumbnail Images */}
            <div className="grid grid-cols-6 gap-3">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative aspect-square bg-gray-50 border-2 transition-all ${
                    selectedImage === index ? 'border-black' : 'border-gray-200 hover:border-gray-400'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} view ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-8">
            <div>
              <p className="text-sm text-gray-500 uppercase tracking-wider font-medium mb-3">{product.brand}</p>
              <h1 className="text-3xl lg:text-4xl font-light text-black mb-6 leading-tight">{product.name}</h1>
              <p className="text-2xl font-semibold text-black">{product.price}</p>
            </div>

            {/* Color Selection */}
            <div>
              <p className="text-sm font-semibold text-black mb-4 tracking-wide">Color</p>
              <div className="flex gap-4">
                {product.colors.map((color) => (
                  <button
                    key={color.name}
                    onClick={() => setSelectedColor(color)}
                    className={`w-10 h-10 border-2 transition-all ${
                      selectedColor.name === color.name ? 'border-black shadow-md' : 'border-gray-300 hover:border-gray-500'
                    }`}
                    style={{ backgroundColor: color.value }}
                    title={color.name}
                  />
                ))}
              </div>
            </div>

            {/* Size Selection */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <p className="text-sm font-semibold text-black tracking-wide">Size</p>
                <button
                  onClick={() => setShowSizeGuide(!showSizeGuide)}
                  className="text-sm text-gray-500 underline hover:text-black transition-colors"
                >
                  Size Guide
                </button>
              </div>
              <div className="grid grid-cols-5 gap-3">
                {product.sizes.map((size) => (
                  <button
                    key={size}
                    onClick={() => setSelectedSize(size)}
                    className={`py-4 text-sm font-medium border-2 transition-all ${
                      selectedSize === size
                        ? 'border-black bg-black text-white'
                        : 'border-gray-300 hover:border-black'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>

            {/* Add to Bag */}
            <div className="space-y-4">
              <button
                className="w-full bg-black text-white py-5 text-sm font-semibold tracking-wide hover:bg-gray-800 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                disabled={!selectedSize}
              >
                {selectedSize ? 'ADD TO BAG' : 'SELECT A SIZE'}
              </button>
              <button
                onClick={() => setIsWishlisted(!isWishlisted)}
                className="w-full border-2 border-black text-black py-5 text-sm font-semibold tracking-wide hover:bg-black hover:text-white transition-colors flex items-center justify-center gap-2"
              >
                {isWishlisted ? (
                  <HeartSolidIcon className="h-5 w-5" />
                ) : (
                  <HeartIcon className="h-5 w-5" />
                )}
                ADD TO WISHLIST
              </button>
            </div>

            {/* Product Details */}
            <div className="space-y-4 border-t pt-6">
              <div>
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="flex items-center justify-between w-full text-left"
                >
                  <span className="text-sm font-medium text-black">Description</span>
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${showDetails ? 'rotate-180' : ''}`} />
                </button>
                {showDetails && (
                  <div className="mt-3 text-sm text-gray-600 leading-relaxed">
                    <p>{product.description}</p>
                    <ul className="mt-4 space-y-1">
                      {product.details.map((detail, index) => (
                        <li key={index}>• {detail}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div className="border-t pt-4">
                <button
                  onClick={() => setShowSizeGuide(!showSizeGuide)}
                  className="flex items-center justify-between w-full text-left"
                >
                  <span className="text-sm font-medium text-black">Size and fit</span>
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${showSizeGuide ? 'rotate-180' : ''}`} />
                </button>
              </div>

              <div className="border-t pt-4">
                <button
                  onClick={() => setShowDelivery(!showDelivery)}
                  className="flex items-center justify-between w-full text-left"
                >
                  <span className="text-sm font-medium text-black">Delivery and Returns</span>
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${showDelivery ? 'rotate-180' : ''}`} />
                </button>
              </div>
            </div>

            {/* Share */}
            <div className="border-t pt-6">
              <button className="flex items-center gap-2 text-sm text-gray-500 hover:text-black">
                <ShareIcon className="h-4 w-4" />
                Share
              </button>
            </div>
          </div>
        </div>

        {/* Recently Viewed */}
        <div className="mt-16 border-t pt-16">
          <h2 className="text-2xl font-light text-black mb-8">Recently Viewed</h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {recentlyViewed.map((item) => (
              <Link key={item.id} href={`/product/${item.id}`} className="group">
                <div className="relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100">
                  <Image
                    src={item.image}
                    alt={item.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-gray-500 uppercase tracking-wide">{item.brand}</p>
                  <h3 className="text-sm font-medium text-black line-clamp-2">{item.name}</h3>
                  <p className="text-sm font-medium text-black">{item.price}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
