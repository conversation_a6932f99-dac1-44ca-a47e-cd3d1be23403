"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst product = {\n    id: 1,\n    name: 'Moon oversized cotton-poplin shirt',\n    brand: 'The Row',\n    price: '£1,200',\n    description: 'An elegant statement of The Row\\'s exacting, tailoring, this cream Moon shirt is cut from crisp cotton-poplin to an oversized fit with clean-lined details. Shown here with The Row Enzo straight-leg leather trousers, The Row Celine calf-hair-lined leather loafers and The Row Celine calf-hair-lined leather loafers.',\n    details: [\n        'Oversized fit',\n        'Button-front closure',\n        'Spread collar',\n        'Long sleeves with button cuffs',\n        'Curved hem',\n        'Made in Italy'\n    ],\n    sizeGuide: 'Size Guide',\n    deliveryReturns: 'Delivery and Returns',\n    images: [\n        '/products/the-row-shirt-1.jpg',\n        '/products/the-row-shirt-2.jpg',\n        '/products/the-row-shirt-3.jpg',\n        '/products/the-row-shirt-4.jpg',\n        '/products/the-row-shirt-5.jpg',\n        '/products/the-row-shirt-6.jpg'\n    ],\n    sizes: [\n        'XS',\n        'S',\n        'M',\n        'L',\n        'XL'\n    ],\n    colors: [\n        {\n            name: 'Cream',\n            value: '#F5F5DC'\n        },\n        {\n            name: 'White',\n            value: '#FFFFFF'\n        },\n        {\n            name: 'Black',\n            value: '#000000'\n        }\n    ]\n};\nconst recentlyViewed = [\n    {\n        id: 2,\n        name: 'Moon oversized cotton-poplin shirt',\n        brand: 'The Row',\n        price: '£1,200',\n        image: '/products/the-row-shirt-recent.jpg'\n    }\n];\nfunction ProductPage() {\n    _s();\n    const [selectedSize, setSelectedSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedColor, setSelectedColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(product.colors[0]);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isWishlisted, setIsWishlisted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSizeGuide, setShowSizeGuide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDelivery, setShowDelivery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"hover:text-black\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/women\",\n                                className: \"hover:text-black\",\n                                children: \"Women\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/women/designers\",\n                                className: \"hover:text-black\",\n                                children: \"Designers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-black\",\n                                children: \"The Row\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 xl:gap-24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-[3/4] bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: product.images[selectedImage],\n                                            alt: product.name,\n                                            fill: true,\n                                            className: \"object-cover\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-6 gap-3\",\n                                        children: product.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedImage(index),\n                                                className: \"relative aspect-square bg-gray-50 border-2 transition-all \".concat(selectedImage === index ? 'border-black' : 'border-gray-200 hover:border-gray-400'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"\".concat(product.name, \" view \").concat(index + 1),\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-10 lg:space-y-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 uppercase tracking-wider font-medium mb-3\",\n                                                children: product.brand\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl lg:text-4xl font-light text-black mb-6 leading-tight\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-black\",\n                                                children: product.price\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-black mb-4 tracking-wide\",\n                                                children: \"Color\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: product.colors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedColor(color),\n                                                        className: \"w-10 h-10 border-2 transition-all \".concat(selectedColor.name === color.name ? 'border-black shadow-md' : 'border-gray-300 hover:border-gray-500'),\n                                                        style: {\n                                                            backgroundColor: color.value\n                                                        },\n                                                        title: color.name\n                                                    }, color.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-black tracking-wide\",\n                                                        children: \"Size\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowSizeGuide(!showSizeGuide),\n                                                        className: \"text-sm text-gray-500 underline hover:text-black transition-colors\",\n                                                        children: \"Size Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-5 gap-3\",\n                                                children: product.sizes.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedSize(size),\n                                                        className: \"py-4 text-sm font-medium border-2 transition-all \".concat(selectedSize === size ? 'border-black bg-black text-white' : 'border-gray-300 hover:border-black'),\n                                                        children: size\n                                                    }, size, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full bg-black text-white py-5 text-sm font-semibold tracking-wide hover:bg-gray-800 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\",\n                                                disabled: !selectedSize,\n                                                children: selectedSize ? 'ADD TO BAG' : 'SELECT A SIZE'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsWishlisted(!isWishlisted),\n                                                className: \"w-full border-2 border-black text-black py-5 text-sm font-semibold tracking-wide hover:bg-black hover:text-white transition-colors flex items-center justify-center gap-2\",\n                                                children: [\n                                                    isWishlisted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"ADD TO WISHLIST\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 border-t pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowDetails(!showDetails),\n                                                        className: \"flex items-center justify-between w-full text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-black\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 transition-transform \".concat(showDetails ? 'rotate-180' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-3 text-sm text-gray-600 leading-relaxed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: product.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"mt-4 space-y-1\",\n                                                                children: product.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"• \",\n                                                                            detail\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowSizeGuide(!showSizeGuide),\n                                                    className: \"flex items-center justify-between w-full text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-black\",\n                                                            children: \"Size and fit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 transition-transform \".concat(showSizeGuide ? 'rotate-180' : '')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowDelivery(!showDelivery),\n                                                    className: \"flex items-center justify-between w-full text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-black\",\n                                                            children: \"Delivery and Returns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 transition-transform \".concat(showDelivery ? 'rotate-180' : '')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-500 hover:text-black\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 border-t pt-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-light text-black mb-8\",\n                                children: \"Recently Viewed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-10 xl:gap-12\",\n                                children: recentlyViewed.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/product/\".concat(item.id),\n                                        className: \"group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: item.image,\n                                                    alt: item.name,\n                                                    fill: true,\n                                                    className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                                                        children: item.brand\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-black line-clamp-2\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-black\",\n                                                        children: item.price\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductPage, \"BEYshFBWZKBHZq62VAoGV6CJ5zA=\");\n_c = ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});