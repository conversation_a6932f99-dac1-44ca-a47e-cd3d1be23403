
'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState, useRef, useEffect } from 'react'
import {
  MagnifyingGlassIcon,
  HeartIcon,
  ShoppingBagIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

const megaMenuItems = {
  women: {
    featured: {
      title: 'NEW SEASON ARRIVALS',
      subtitle: 'Discover the latest from your favorite designers',
      image: '/hero-image.jpg',
      link: '/women/new-arrivals'
    },
    categories: [
      {
        name: 'Clothing',
        image: '/category-clothing.jpg',
        items: ['Shop All', 'Dresses', 'Knitwear', 'Coats', 'Jackets', 'Jeans', 'Jumpsuits', 'Lingerie', 'Skirts', 'Suits', 'Swimwear', 'Tops', 'Trousers']
      },
      {
        name: 'Shoes',
        image: '/category-shoes.jpg',
        items: ['Shop All', 'Boots', 'Flats', 'Heels', 'Sandals', 'Sneakers', 'Trainers']
      },
      {
        name: 'Bags',
        image: '/category-bags.jpg',
        items: ['Shop All', 'Backpacks', 'Belt Bags', 'Clutches', 'Cross-body', 'Handbags', 'Shoulder Bags', 'Totes', 'Travel']
      },
      {
        name: 'Accessories',
        image: '/category-accessories.jpg',
        items: ['Shop All', 'Belts', 'Gloves', 'Hair Accessories', 'Hats', 'Jewelry & Watches', 'Scarves', 'Sunglasses', 'Tech Accessories']
      }
    ],
    editorial: {
      title: 'WINTER ESSENTIALS',
      subtitle: 'Curated edit for the season',
      image: '/editorial-1.jpg',
      link: '/women/winter-essentials'
    },
    designers: ['Acne Studios', 'Bottega Veneta', 'Ganni', 'Jacquemus', 'Khaite', 'Loewe', 'Prada', 'Saint Laurent', 'The Row', 'Toteme']
  },
  men: {
    featured: {
      title: 'NEW SEASON ARRIVALS',
      subtitle: 'Discover the latest from your favorite designers',
      image: '/hero-image.jpg',
      link: '/men/new-arrivals'
    },
    categories: [
      {
        name: 'Clothing',
        image: '/category-clothing.jpg',
        items: ['Shop All', 'Blazers', 'Coats', 'Hoodies', 'Jackets', 'Jeans', 'Knitwear', 'Polo Shirts', 'Shirts', 'Shorts', 'Suits', 'Sweatshirts', 'T-Shirts', 'Trousers']
      },
      {
        name: 'Shoes',
        image: '/category-shoes.jpg',
        items: ['Shop All', 'Boots', 'Dress Shoes', 'Loafers', 'Sandals', 'Sneakers', 'Trainers']
      },
      {
        name: 'Bags',
        image: '/category-bags.jpg',
        items: ['Shop All', 'Backpacks', 'Belt Bags', 'Briefcases', 'Cross-body', 'Messenger', 'Totes', 'Travel']
      },
      {
        name: 'Accessories',
        image: '/category-accessories.jpg',
        items: ['Shop All', 'Belts', 'Gloves', 'Hats', 'Jewelry & Watches', 'Scarves', 'Sunglasses', 'Tech Accessories', 'Ties', 'Wallets']
      }
    ],
    editorial: {
      title: 'MODERN CLASSICS',
      subtitle: 'Timeless pieces for the modern man',
      image: '/editorial-2.jpg',
      link: '/men/modern-classics'
    },
    designers: ['Acne Studios', 'Bottega Veneta', 'Fear of God', 'Jacquemus', 'Kenzo', 'Off-White', 'Stone Island', 'Thom Browne', 'The Row', 'Valentino']
  }
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [activeMegaMenu, setActiveMegaMenu] = useState<string | null>(null)
  const [expandedMobileMenu, setExpandedMobileMenu] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const megaMenuRef = useRef<HTMLDivElement>(null)
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target as Node)) {
        setActiveMegaMenu(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      // Cleanup timeout on unmount
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current)
      }
    }
  }, [])

  const handleMegaMenuEnter = (menu: string) => {
    if (!isMobile) {
      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current)
        hoverTimeoutRef.current = null
      }
      setActiveMegaMenu(menu)
    }
  }

  const handleMegaMenuLeave = () => {
    if (!isMobile) {
      // Add a delay before closing the menu
      hoverTimeoutRef.current = setTimeout(() => {
        setActiveMegaMenu(null)
      }, 200) // Increased delay for better UX
    }
  }

  const handleMegaMenuContentEnter = () => {
    // Clear timeout when entering the mega menu content
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
      hoverTimeoutRef.current = null
    }
  }

  const handleMegaMenuContentLeave = () => {
    // Close menu when leaving the mega menu content
    if (!isMobile) {
      hoverTimeoutRef.current = setTimeout(() => {
        setActiveMegaMenu(null)
      }, 100) // Shorter delay when leaving content area
    }
  }

  // Additional handler for when mouse leaves the entire header area
  const handleHeaderLeave = () => {
    if (!isMobile) {
      hoverTimeoutRef.current = setTimeout(() => {
        setActiveMegaMenu(null)
      }, 100)
    }
  }

  const toggleMobileSubmenu = (menu: string) => {
    setExpandedMobileMenu(expandedMobileMenu === menu ? null : menu)
  }

  return (
    <>
      {/* Main Header */}
      <header
        className="bg-white border-b border-gray-200 sticky top-0 z-50"
        ref={megaMenuRef}
        onMouseLeave={handleHeaderLeave}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-black hover:text-gray-600 transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <XMarkIcon className="h-5 w-5" /> : <Bars3Icon className="h-5 w-5" />}
            </button>

            {/* Logo */}
            <Link href="/" className="flex-shrink-0">
              <h1 className="text-xl md:text-2xl font-bold tracking-[0.2em] text-black">MATCHES</h1>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-10 xl:space-x-16">
              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('women')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/women" className="text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center">
                  Women
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('men')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/men" className="text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center">
                  Men
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <Link href="/designers" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Designers
              </Link>
              <Link href="/sale" className="text-sm font-medium text-red-600 hover:text-red-700 transition-colors">
                Sale
              </Link>
            </nav>

            {/* Right side icons */}
            <div className="flex items-center space-x-4 lg:space-x-6">
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="p-2 text-black hover:text-gray-600 transition-colors lg:flex"
                aria-label="Search"
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
              </button>

              <Link
                href="/account"
                className="p-2 text-black hover:text-gray-600 transition-colors hidden lg:flex"
                aria-label="Account"
              >
                <UserIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/wishlist"
                className="p-2 text-black hover:text-gray-600 transition-colors relative hidden lg:flex"
                aria-label="Wishlist"
              >
                <HeartIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/bag"
                className="p-2 text-black hover:text-gray-600 transition-colors relative"
                aria-label="Shopping bag"
              >
                <ShoppingBagIcon className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Search Bar */}
          {isSearchOpen && (
            <div className="absolute top-full left-0 right-0 bg-white border-t border-gray-200 py-4 z-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center gap-4">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      placeholder="Search for products, designers..."
                      className="w-full px-4 py-3 border border-gray-300 bg-white text-black focus:outline-none focus:border-black text-sm"
                      autoFocus
                    />
                    <MagnifyingGlassIcon className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
                  <button
                    onClick={() => setIsSearchOpen(false)}
                    className="text-sm font-medium hover:text-gray-600 text-black px-4"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Rich Visual Mega Menu */}
        {activeMegaMenu && megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems] && (
          <div
            className="absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-xl z-40"
            onMouseEnter={handleMegaMenuContentEnter}
            onMouseLeave={handleMegaMenuContentLeave}
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <div className="grid grid-cols-12 gap-8">
                {/* Featured Section */}
                <div className="col-span-3">
                  <Link
                    href={megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].featured.link}
                    className="group block"
                  >
                    <div className="relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100">
                      <Image
                        src={megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].featured.image}
                        alt={megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].featured.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <h3 className="text-lg font-semibold mb-2 tracking-wide">
                          {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].featured.title}
                        </h3>
                        <p className="text-sm opacity-90">
                          {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].featured.subtitle}
                        </p>
                      </div>
                    </div>
                  </Link>
                </div>

                {/* Categories with Images */}
                <div className="col-span-6">
                  <div className="grid grid-cols-2 gap-8">
                    {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].categories.map((category) => (
                      <div key={category.name} className="space-y-4">
                        <Link
                          href={`/${activeMegaMenu}/${category.name.toLowerCase()}`}
                          className="group block"
                        >
                          <div className="relative aspect-[4/3] mb-3 overflow-hidden bg-gray-100">
                            <Image
                              src={category.image}
                              alt={category.name}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-500"
                            />
                          </div>
                          <h3 className="text-sm font-semibold text-black uppercase tracking-wide mb-3 group-hover:text-gray-600 transition-colors">
                            {category.name}
                          </h3>
                        </Link>
                        <div className="space-y-1">
                          {category.items.slice(0, 6).map((item, index) => (
                            <Link
                              key={`${category.name}-${item}-${index}`}
                              href={`/${activeMegaMenu}/${category.name.toLowerCase()}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                              className="block text-xs text-gray-600 hover:text-black transition-colors py-1"
                            >
                              {item}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Editorial & Designers */}
                <div className="col-span-3 space-y-8">
                  {/* Editorial Section */}
                  <div>
                    <Link
                      href={megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].editorial.link}
                      className="group block"
                    >
                      <div className="relative aspect-[4/3] mb-4 overflow-hidden bg-gray-100">
                        <Image
                          src={megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].editorial.image}
                          alt={megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].editorial.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div className="absolute bottom-4 left-4 text-white">
                          <h3 className="text-sm font-semibold mb-1 tracking-wide">
                            {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].editorial.title}
                          </h3>
                          <p className="text-xs opacity-90">
                            {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].editorial.subtitle}
                          </p>
                        </div>
                      </div>
                    </Link>
                  </div>

                  {/* Featured Designers */}
                  <div>
                    <h3 className="text-sm font-semibold text-black uppercase tracking-wide mb-4">
                      Featured Designers
                    </h3>
                    <div className="space-y-2">
                      {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].designers.slice(0, 8).map((designer, index) => (
                        <Link
                          key={`${designer}-${index}`}
                          href={`/designers/${designer.toLowerCase().replace(/\s+/g, '-')}`}
                          className="block text-xs text-gray-600 hover:text-black transition-colors py-1"
                        >
                          {designer}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
              {/* Women Menu */}
              <div className="border-b border-gray-200 pb-4 mb-4">
                <button
                  onClick={() => toggleMobileSubmenu('women')}
                  className="flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black"
                >
                  Women
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'women' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'women' && (
                  <div className="pl-4 space-y-2 mt-2">
                    <Link href="/women/new-in" className="block py-2 text-sm text-gray-600 hover:text-black">New In</Link>
                    <Link href="/women/clothing" className="block py-2 text-sm text-gray-600 hover:text-black">Clothing</Link>
                    <Link href="/women/shoes" className="block py-2 text-sm text-gray-600 hover:text-black">Shoes</Link>
                    <Link href="/women/bags" className="block py-2 text-sm text-gray-600 hover:text-black">Bags</Link>
                    <Link href="/women/accessories" className="block py-2 text-sm text-gray-600 hover:text-black">Accessories</Link>
                  </div>
                )}
              </div>

              {/* Men Menu */}
              <div className="border-b border-gray-200 pb-4 mb-4">
                <button
                  onClick={() => toggleMobileSubmenu('men')}
                  className="flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black"
                >
                  Men
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'men' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'men' && (
                  <div className="pl-4 space-y-2 mt-2">
                    <Link href="/men/new-in" className="block py-2 text-sm text-gray-600 hover:text-black">New In</Link>
                    <Link href="/men/clothing" className="block py-2 text-sm text-gray-600 hover:text-black">Clothing</Link>
                    <Link href="/men/shoes" className="block py-2 text-sm text-gray-600 hover:text-black">Shoes</Link>
                    <Link href="/men/bags" className="block py-2 text-sm text-gray-600 hover:text-black">Bags</Link>
                    <Link href="/men/accessories" className="block py-2 text-sm text-gray-600 hover:text-black">Accessories</Link>
                  </div>
                )}
              </div>

              <div className="space-y-3 border-b border-gray-200 pb-4 mb-4">
                <Link href="/just-in" className="block py-2 text-sm font-medium text-black">Just In</Link>
                <Link href="/designers" className="block py-2 text-sm font-medium text-black">Designers</Link>
                <Link href="/sale" className="block py-2 text-sm font-medium text-red-600">Sale</Link>
              </div>

              <div className="space-y-3">
                <Link href="/account" className="flex items-center py-2 text-sm font-medium text-black">
                  <UserIcon className="h-4 w-4 mr-3" />
                  My Account
                </Link>
                <Link href="/wishlist" className="flex items-center py-2 text-sm font-medium text-black">
                  <HeartIcon className="h-4 w-4 mr-3" />
                  Wishlist
                </Link>
              </div>
            </div>
          </div>
        )}
      </header>
    </>
  )
}
